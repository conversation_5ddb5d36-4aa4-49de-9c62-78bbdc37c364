<template>
  <q-layout view="lHh Lpr lFf">
    <q-page-container>
      <q-page class="flex">
        <BlogHeader :theme="theme" :speakerStatusChange="speakerStatusChange" :speakerArr="speakerArr" :openConsecutive="openConsecutive" :consecutiveInterpretationClick="consecutiveInterpretationClick" :langList="langList" :glossaryRes="glossaryRes" :glossarySceneId="glossarySceneId" :checkIsGlossaryScene="checkIsGlossaryScene" :replaceRes="replaceRes" :replaceSceneId="replaceSceneId" :checkIsReplaceScene="checkIsReplaceScene" :changeGlossaryScene="changeGlossaryScene" :isShowRight="isShowRight" :isShowRightButton="isShowRightButton" />
        <Translation ref="translation" :openConsecutive="openConsecutive" :speakerArr="speakerArr" :hideSpeaker="hideSpeaker" :autoScroll="asrResultAutoScroll" :translationRes="translationRes" :fontSize="fontSize" :theme="theme" @dragenter="handleDragEnter" :isStart="isStart" />
        <div class="footerBox">
          <el-tooltip class="box-item" effect="dark" content="Settings" placement="top-start">
            <el-button :class="theme ? 'themeWhite' : 'themeDark'" icon="Tools" circle @click="openSetting"></el-button>
          </el-tooltip>
          <el-tooltip class="box-item" effect="dark" content="Bilingual Reader" placement="top-start">
            <el-button :class="theme ? 'themeWhite' : 'themeDark'" icon="Memo" circle @click="openFileTranslate"></el-button>
          </el-tooltip>
           <!-- <el-tooltip class="box-item" effect="dark" content="Bilingual Reader" placement="top-start">
            <el-button :class="theme ? 'themeWhite' : 'themeDark'" icon="open" circle @click="openRealtimeTranslate"></el-button>
          </el-tooltip> -->
          <el-tooltip class="box-item" effect="dark" content="Glossary Management" placement="top-start">
            <el-button :class="theme ? 'themeWhite' : 'themeDark'" icon="List" circle @click="openGlossaryScene"></el-button>
          </el-tooltip>
          <el-tooltip class="box-item" effect="dark" content="Force Replacement" placement="top-start">
            <el-button :class="theme ? 'themeWhite' : 'themeDark'" circle @click="openReplace">
              <svg width="14" height="14" fill="currentColor">
                <svg viewBox="0 0 200 200" xml:space="preserve">
                  <path fill="# "
                    d="M189.3,155.8c-1.7-2.7-4-5.1-6.6-7c2.7-4,4.3-9,4.3-14.2c0-12.7-8.8-22.9-19.7-22.9h-33.2c-6,0-10.9,4.9-10.9,10.9v62.6 c0,6,4.9,10.9,10.9,10.9h33c7.1,0,13.7-2.8,18.7-7.8c5-5,7.7-11.5,7.6-18.5C193.4,164.8,192,159.9,189.3,155.8L189.3,155.8z M175.4,178c-2.2,2.2-5.1,3.4-8.1,3.4h-29.5v-23.1h29.5c3,0,5.9,1.2,8.1,3.4c2.2,2.2,3.4,5.1,3.4,8.1S177.6,175.8,175.4,178 L175.4,178z M170.2,141.1c-0.4,0.5-1.6,1.7-2.9,1.7h-29.5v-16.6h29.5c1.4,0,2.5,1.2,2.9,1.7c1.4,1.6,2.2,4.1,2.2,6.6 C172.4,137,171.6,139.5,170.2,141.1L170.2,141.1z M89.6,82.8L63.3,16.9c-1.1-2.8-3.9-13-13.4-13l0,0c-9.7,0-12.4,10.2-13.5,13 L10.2,82.4c-1.5,3.8,0.1,8.3,3.8,9.9c3.8,1.6,8.2-0.2,9.8-4.1l8.7-21.7h34.8l8.7,21.7c1.2,2.9,3.9,4.7,6.9,4.7 c0.9,0,1.9-0.2,2.8-0.5C89.3,90.9,91.2,86.6,89.6,82.8z M61.3,51.8H38.4l11.5-30.9 M109.5,171.6l-23.4,13.8c-1.2,0.7-2.5,1-3.8,1 c-2.5,0-5-1.3-6.4-3.6c-2.1-3.5-0.9-8.1,2.6-10.1l8-4.7c-9.7-3-18.2-7.2-25.5-12.8c-8.3-6.2-14.9-14-19.8-23.2 c-8.2-15.5-8.4-29.6-8.4-30.2v0c0-3.2,2-6.1,5-7c5.1-1.6,9.8,2.2,9.8,7v0c0,0.1,0.2,11.5,6.9,23.9c3.8,7.1,9.1,13.1,15.6,17.9 c6,4.4,13,7.9,21.1,10.3l-4.7-9.1c-1.9-3.6-0.5-8.1,3.1-10c3.6-1.9,8.1-0.5,10,3.1l12.5,23.9C114.2,165.3,112.9,169.6,109.5,171.6z">
                  </path>
                </svg>
              </svg>
            </el-button>
          </el-tooltip>
          <el-tooltip class="box-item" effect="dark" content="Delete Text" placement="top-start">
            <el-button :disabled="isStart" :class="theme ? 'themeWhite' : 'themeDark'" icon="Delete" circle @click="clearTranslationRes">
            </el-button>
          </el-tooltip>
          <el-button :class="theme ? 'themeWhite' : 'themeDark'" circle icon="mic" :style="{
                            background: `linear-gradient(to top, #67C23A 0%, #E6A23C ${inputDeviceVolume }%, #F56C6C ${inputDeviceVolume}%, transparent ${inputDeviceVolume}%)`,
                            transition: 'background 0.3s ease'
                        }">
          </el-button>
          <div class="microphoneBox">
            <el-select v-model="microphone" filterable>
              <el-option v-for="item in audioInputDeviceList" :key="item.value" :label="item.name" :value="item" />
            </el-select>
          </div>
          <el-button class="langBtn" type='primary' @click="handleChangeLang">{{ lang.show }}</el-button>
          <el-button v-if="!isStart" type="primary" :loading="startAsrLoading" icon="CaretRight" @click="debouncedStartTranslation">Start</el-button>
          <el-button v-else type="danger" icon="Microphone" :loading="stopAsrLoading" @click="endTranslation">Stop</el-button>
          <div class="loudspeaker q-mr-sm">
            <el-tooltip class="box-item" effect="dark" :content="isRealtimeTranslateOpen ? 'Close Realtime Translate' : 'Open Realtime Translate'" placement="top-start">
              <el-button 
                :class="[theme ? 'themeWhite' : 'themeDark', isRealtimeTranslateOpen ? 'active' : '']" 
                circle 
                @click="toggleRealtimeTranslate">

                <svg v-if="isRealtimeTranslateOpen" t="1748566053120" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1950" width="16" height="16"><path d="M426.666667 921.6c-161.3824 0-295.253333-106.154667-319.863467-244.497067l34.7136 34.7136a17.134933 17.134933 0 0 0 24.1664 0 17.066667 17.066667 0 0 0 0-24.132266l-68.266667-68.266667a17.271467 17.271467 0 0 0-5.597866-3.720533c-1.194667-0.477867-2.423467-0.546133-3.6864-0.750934C87.210667 614.8096 86.3232 614.4 85.333333 614.4c-1.297067 0-2.4576 0.477867-3.652266 0.750933-0.9216 0.2048-1.911467 0.2048-2.798934 0.580267a17.885867 17.885867 0 0 0-5.700266 3.7888L5.0176 687.684267a17.066667 17.066667 0 0 0 24.132267 24.132266l41.8816-41.8816C92.16 830.634667 243.438933 955.733333 426.666667 955.733333a17.066667 17.066667 0 0 0 0-34.133333z m198.485333-1.194667a17.066667 17.066667 0 0 0 22.152533-9.5232L683.997867 819.2h168.0384l36.693333 91.682133a16.9984 16.9984 0 0 0 22.186667 9.5232 17.066667 17.066667 0 0 0 9.489066-22.186666l-136.533333-341.333334c-5.154133-12.970667-26.555733-12.970667-31.675733 0l-136.533334 341.333334a17.066667 17.066667 0 0 0 9.489067 22.186666z m142.848-311.227733L838.3488 785.066667h-140.731733l70.382933-175.889067zM631.466667 102.4c144.452267 0 264.260267 106.222933 286.208 244.6336l-35.293867-34.9184a17.066667 17.066667 0 1 0-23.995733 24.2688l66.7648 66.082133A16.827733 16.827733 0 0 0 938.666667 409.6l0.341333-0.068267 0.375467 0.068267c1.604267-0.170667 3.1744-0.648533 4.744533-1.092267 0.4096-0.136533 0.887467-0.136533 1.262933-0.3072a15.9744 15.9744 0 0 0 6.109867-3.720533l67.549867-68.266667a17.066667 17.066667 0 0 0-24.2688-23.995733l-41.540267 41.984C934.1952 193.4336 797.252267 68.266667 631.466667 68.266667a17.066667 17.066667 0 0 0 0 34.133333z m-204.8 68.266667H307.2V119.466667a17.066667 17.066667 0 0 0-34.133333 0V170.666667H153.6a17.066667 17.066667 0 0 0 0 34.133333h18.7392c8.6016 50.8928 52.565333 109.738667 94.4128 155.921067a875.861333 875.861333 0 0 1-55.944533 52.9408 17.066667 17.066667 0 0 0 22.1184 26.0096c2.6624-2.2528 27.2384-23.381333 57.207466-54.033067 29.969067 30.685867 54.5792 51.780267 57.207467 54.033067a17.066667 17.066667 0 1 0 22.1184-26.0096 848.554667 848.554667 0 0 1-55.944533-52.9408C355.362133 314.538667 399.325867 255.6928 407.927467 204.8h18.7392a17.066667 17.066667 0 0 0 0-34.133333z m-136.533334 165.0688C252.2112 293.614933 215.8592 244.394667 207.018667 204.8h166.1952C364.407467 244.394667 328.055467 293.614933 290.133333 335.735467zM1006.933333 477.866667H546.133333V17.066667a17.066667 17.066667 0 0 0-17.066666-17.066667H17.066667a17.066667 17.066667 0 0 0-17.066667 17.066667v512a17.066667 17.066667 0 0 0 17.066667 17.066666H477.866667v460.8a17.066667 17.066667 0 0 0 17.066666 17.066667h512a17.066667 17.066667 0 0 0 17.066667-17.066667v-512a17.066667 17.066667 0 0 0-17.066667-17.066666z m-529.066666 17.066666v17.066667H34.133333V34.133333h477.866667v443.733334h-17.066667a17.066667 17.066667 0 0 0-17.066666 17.066666zM989.866667 989.866667H512V512h477.866667v477.866667z" fill="#ffffff" p-id="1951"></path></svg>
                <svg v-if="!isRealtimeTranslateOpen" t="1748566001668" class="icon" viewBox="0 0 1027 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1593" width="16" height="16"><path d="M291.84 334.506667c37.546667-40.96 75.093333-92.16 81.92-129.706667H209.92c6.826667 40.96 44.373333 88.746667 81.92 129.706667z" fill="#07c160" p-id="1594"></path><path d="M530.773333 443.733333c10.24 0 17.066667-6.826667 17.066667-17.066666V17.066667c0-10.24-6.826667-17.066667-17.066667-17.066667H18.773333C8.533333 0 1.706667 6.826667 1.706667 17.066667v512c0 10.24 6.826667 17.066667 17.066666 17.066666h409.6c10.24 0 17.066667-6.826667 17.066667-17.066666v-34.133334c0-27.306667 23.893333-51.2 51.2-51.2h34.133333z m-102.4-238.933333H411.306667c-10.24 51.2-51.2 109.226667-95.573334 157.013333 20.48 23.893333 40.96 40.96 54.613334 54.613334 6.826667 6.826667 6.826667 17.066667 3.413333 23.893333-3.413333 3.413333-6.826667 6.826667-13.653333 6.826667-3.413333 0-6.826667 0-10.24-3.413334-3.413333-3.413333-27.306667-23.893333-58.026667-54.613333-30.72 30.72-54.613333 51.2-58.026667 54.613333h-10.24c-3.413333 0-10.24-3.413333-13.653333-6.826666-6.826667-6.826667-6.826667-17.066667 3.413333-23.893334 13.653333-10.24 34.133333-30.72 54.613334-54.613333C226.986667 314.026667 182.613333 256 172.373333 204.8h-17.066666c-10.24 0-17.066667-6.826667-17.066667-17.066667s6.826667-17.066667 17.066667-17.066666H274.773333V119.466667c0-10.24 6.826667-17.066667 17.066667-17.066667s17.066667 6.826667 17.066667 17.066667V170.666667h119.466666c10.24 0 17.066667 6.826667 17.066667 17.066666s-6.826667 17.066667-17.066667 17.066667z" fill="#07c160" p-id="1595"></path><path d="M1008.64 477.866667h-512c-10.24 0-17.066667 6.826667-17.066667 17.066666v512c0 10.24 6.826667 17.066667 17.066667 17.066667h512c10.24 0 17.066667-6.826667 17.066667-17.066667v-512c0-10.24-6.826667-17.066667-17.066667-17.066666z m-95.573333 443.733333h-6.826667c-6.826667 0-13.653333-3.413333-17.066667-10.24L855.04 819.2h-170.666667l-34.133333 92.16c-3.413333 10.24-13.653333 13.653333-20.48 10.24-10.24-3.413333-13.653333-13.653333-10.24-23.893333l136.533333-341.333334c6.826667-13.653333 27.306667-13.653333 30.72 0l136.533334 341.333334c3.413333 10.24-3.413333 20.48-10.24 23.893333z" fill="#07c160" p-id="1596"></path><path d="M698.026667 785.066667h143.36l-71.68-177.493334zM633.173333 102.4c143.36 0 262.826667 105.813333 286.72 245.76l-34.133333-34.133333c-6.826667-6.826667-17.066667-6.826667-23.893333 0-6.826667 6.826667-6.826667 17.066667 0 23.893333l68.266666 64.853333c3.413333 3.413333 6.826667 6.826667 13.653334 6.826667h3.413333c3.413333 0 3.413333-3.413333 6.826667-3.413333l68.266666-68.266667c6.826667-6.826667 6.826667-17.066667 0-23.893333s-17.066667-6.826667-23.893333 0L957.44 354.986667C936.96 194.56 800.426667 68.266667 633.173333 68.266667c-10.24 0-17.066667 6.826667-17.066666 17.066666s6.826667 17.066667 17.066666 17.066667zM428.373333 921.6c-160.426667 0-296.96-105.813333-320.853333-245.76l34.133333 34.133333c3.413333 6.826667 10.24 6.826667 13.653334 6.826667s10.24 0 13.653333-3.413333c6.826667-6.826667 6.826667-17.066667 0-23.893334l-68.266667-68.266666s-3.413333-3.413333-6.826666-3.413334c-3.413333-3.413333-10.24-3.413333-13.653334-3.413333-3.413333 0-3.413333 3.413333-6.826666 3.413333l-68.266667 68.266667c-6.826667 6.826667-6.826667 17.066667 0 23.893333s17.066667 6.826667 23.893333 0L69.973333 669.013333c23.893333 160.426667 174.08 286.72 358.4 286.72 10.24 0 17.066667-6.826667 17.066667-17.066666s-6.826667-17.066667-17.066667-17.066667z" fill="#07c160" p-id="1597"></path></svg>
              </el-button>
            </el-tooltip>
          </div>
          
          <el-tooltip class="box-item" effect="dark" content="Volume" placement="top-start">
            <el-popover placement="bottom" @before-enter="getSpeakerVolume" :width="200">
              <template #reference>
                <div class="">
                  <el-button :class="theme ? 'themeWhite' : 'themeDark'" icon="Headset" circle></el-button>
                </div>
              </template>
              <div class="slider-demo-block">
                <span class="demonstration">Volume</span>
                <el-slider size="small" :show-tooltip="false" :min="1" :max="100" :step="1" @change="loudspeakerChange" v-model="loudspeakerValue" />
              </div>
            </el-popover>
          </el-tooltip>
        </div>

        <Setting ref="settingRef" :theme="theme" :openThirdlyLanguageStatus="openThirdlyLanguageStatus" :color="color" :fontSize="fontSize" :langList="langList" :changeColor="changeColor" :changeFontSize="changeFontSize" :changeTheme="changeTheme" :changeOpenThirdlyLanguage="changeOpenThirdlyLanguage" :handleMainLanguage="handleMainLanguage" :handleSecondLanguage="handleSecondLanguage" :handleChangeSecondAsrEngineParent="handleChangeSecondAsrEngineParent" :handleChangeThirdlyAsrEngineParent="handleChangeThirdlyAsrEngineParent" :handleChangeMainAsrEngineParent="handleChangeMainAsrEngineParent" :handleAsrPatternChange="handleAsrPatternChange" />
        <GlossaryScene :glossarySceneId="glossarySceneId" :changeGlossaryScene="changeGlossaryScene" :glossarySceneVisible="glossarySceneVisible" :closeGlossaryScene="closeGlossaryScene" :langList="langList" :checkIsGlossaryScene="checkIsGlossaryScene" :clearGlossarySceneId="clearGlossarySceneId" />
        <ReplaceScene :replaceSceneId="replaceSceneId" :changeReplaceScene="changeReplaceScene" :replaceSceneVisible="replaceSceneVisible" :closeReplaceScene="closeReplaceScene" :langList="langList" :checkIsReplaceScene="checkIsReplaceScene" :clearReplaceSceneId="clearReplaceSceneId" />
        <el-dialog v-model="showInstallGTEAudio" title="Tips" width="500">
          <span>Install GTE Audio Driver before you can use System Audio</span>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="showInstallGTEAudio = false">Cancel</el-button>
              <el-button type="primary" @click="installGTEAudio">
                Confirm
              </el-button>
            </div>
          </template>
        </el-dialog>
      </q-page>
    </q-page-container>
  </q-layout>

</template>
<script setup>
import { ref, onMounted, watch, onBeforeUnmount, onUnmounted, nextTick, computed } from 'vue'
import { getSetting, saveSetting } from "@/api/SettingApi";
import BlogHeader from "@/pages/components/BlogHeader.vue";
import Translation from "@/pages/components/Translation.vue";
import Setting from "@/pages/components/Setting.vue";
import store from "@/store";
import moment from "moment";
import GlossaryScene from "@/pages/Glossary/GlossaryScene.vue";
import ReplaceScene from "@/pages/Replace/ReplaceScene.vue";
import { getGlossary, getGlossaryByIds, getAllGlossaryScene } from "@/api/GlossaryApi";
import { getReplace } from "@/api/ReplaceApi";
import { checkTerpMetaTrial } from "@/api/LoginApi"
import { errorRecord } from '@/api/LogRecord'
import { refreshTranslation, switchTranslation } from "@/utils/translationUtils";
import { debounce } from '@/utils/debounce';
import DetectFun from "@/utils/microphoneVolume"
import 'animate.css';
import { translateSearch } from "@/api/TranslateApi";
import { getTranslateModelList } from "@/api/TranslateApi";
import { useRouter } from "vue-router";
import setting from '@/store/modules/setting';
import { Platform } from 'quasar'
const { ipcRenderer } = require('electron')
const macAudioDevices = require('@spotxyz/macos-audio-devices');
import xunfeiClient from '@/utils/xunfeiClient';
import boothWebsocketClient from '@/utils/boothWsClient';
import { ElMessage } from "element-plus";
import {getTextAfterResult,getTextBeforeFifthPunctuation,mapSpeaker} from "@/utils/commonUtils";
import { getAzureAsrToken } from "@/api/AsrApi";
import AsrProcessor from "@/utils/asrProcessor";
const SpeechSDK = require("microsoft-cognitiveservices-speech-sdk");
const router = useRouter()
const settingRef = ref(null);
const startAsrLoading = ref(false)
const stopAsrLoading = ref(false)
// ASR处理器实例
const asrProcessor = new AsrProcessor()
// 翻译结果数据集
const translationRes = ref([])
// 每一个翻译结果的唯一标识 自增
const translationId = ref(0)
// 用户ui设置
const theme = ref('true')
const color = ref('#FF4500')
const fontSize = ref(28)
const showInstallGTEAudio = ref(false)
const allGlossarySceneData = ref([])
//交传模式
const openConsecutive = ref(false)
const asrResultAutoScroll = ref(true)
//扬声器音量调整
const loudspeakerValue = ref(80)
const loudspeakerChange = (newValue) => {
  ipcRenderer.send('changeLoudSpeakerVolume', { volume: newValue })
  console.log('滑块的值已改变为:', newValue);
};
const getSpeakerVolume = async () => {
  if (Platform.is.win) return
  const volume = await ipcRenderer.invoke('getSystemSpeakerVolume')
  console.log('弹出框已显示', volume);
  loudspeakerValue.value = volume
};
// 翻译语言
const langList = ref()
const lang = ref({
  code: 'en-US',
  show: 'English-United States',
  isShow: false,
  main: true
})

const lastExecutionTime = ref(0)
const volumeMic = ref(0)
// 词汇场景页面显隐
const glossarySceneVisible = ref(false)
// 用户选择的当前翻译的词汇场景id
const glossarySceneId = ref()
// 替换场景页面显隐
const replaceSceneVisible = ref(false)
// 用户选择的当前翻译的替换场景id
const replaceSceneId = ref()
// 子组件（中间翻译部分）的 ref
const translation = ref(null)
// 是否展示画板
const isShowRight = ref(false)
// 是否展示画板控制的按钮
const isShowRightButton = ref(false)
const drawCanvas = ref(null)
const topButton = ref(null)
const disX = ref() // 按钮的位置
const disY = ref() // 按钮的位置
const isDragButton = ref(false) // 是否正在拖拽按钮
const isSwitch = ref(false) // 是否转换
const firstStart = ref(false)
const uploading = ref(false) // 拖拽上传弹框控制参数
const isStart = ref(false)
// 词汇场景下的词汇数据
const glossaryRes = ref([])
const replaceRes = ref([])
const microphone = ref()
const audioInputDeviceList = ref([])
const searchValue = ref(null)
const translateBox = ref(null)
//语音识别方式
const asrType = ref(1)
const checkInterval = ref(null)
const platform = computed(() => {
  return Platform.is.win ? 'win' : Platform.is.mac ? 'mac' : null
});
//输入设备音量检测
const microphoneDetect = ref(null)
const inputDeviceVolume = ref(0)
//微软asr
const azureRecognizer = ref(null)
const transcriberRecognizer = ref(null)
const azureSubscriptionKey = ref('05262b7addbd46c0858511ecc4ee9491')
const azureRegion = ref('centralus')
// const azureSubscriptionKey = ref('2734f2ef2b4e460089fa2340950f7df7')
// const azureRegion = ref('japanwest')
const languageRegion = ref('zh-CN')
const audioChangeElMessage = ref(null)
const speakerArr = ref([])
//讯飞asr
const xunfeiInterval = ref(null)
//asr开启时的设备信息
const asrDeviceInfo = ref(null)
const isRealtimeTranslateOpen = ref(false)
//是否使用第三语言
const openThirdlyLanguageStatus = ref(false)
watch(
  speakerArr,
  (newVal, oldVal) => {
    console.log('speakerArr changed', newVal)
    ipcRenderer.send('speaker-hide-status-change', JSON.stringify(newVal))
  }
)
watch(
  microphone,
  async (newValue, oldValue) => {
    if (isStart.value) {
      isStart.value = false
      await stopAsr()
      while (startAsrLoading.value) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      startAsr()
    }
  }
)
const debouncedRestart = debounce(() => {
  restartMicrosoftOnlineAsr();
}, 300);
watch(
  glossaryRes,
  (newGlossaryRes, oldGlossaryRes) => {
    // 更新翻译结果集
    debouncedRestart()
    refreshTranslation(replaceRes.value, glossaryRes.value, translationRes.value, lang.value)
  }
)
watch(
  replaceRes,
  (newReplaceRes, oldReplaceRes) => {
    // 更新翻译结果集
    refreshTranslation(replaceRes.value, glossaryRes.value, translationRes.value, lang.value)
  }
)

//交传模式改变
const consecutiveInterpretationClick = async ()=>{
    openConsecutive.value = !openConsecutive.value
    console.log('openConsecutive ',openConsecutive.value)
    if (isStart.value) {
      isStart.value = false;
      await stopAsr()
      while (startAsrLoading.value) {
          await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
 }
const handleDeviceChange = (event) => {
  console.log('+++++++++++++handleDeviceChange')
  //设备变化时，如果不是system audio模式，直接停止asr
  if (microphone.value && microphone.value.name != 'System Audio') {
    endTranslation()
  }
  //  navigator.mediaDevices.addEventListener('devicechange', async (event)=>{
  const currentTime = new Date().getTime();
  // 如果距离上次执行时间少于500毫秒，则不执行
  if (currentTime - lastExecutionTime.value < 500) {
    return;
  }
  if (audioChangeElMessage.value) {
    audioChangeElMessage.value.close()
  }
  audioChangeElMessage.value = ElMessage({
    message: 'Audio Device Change.',
    type: 'warning',
  })
  lastExecutionTime.value = currentTime;
  console.log('device detection 音频设备发生变化:', event, ' ', new Date().getTime());
  setTimeout(async () => {
    await setAudioInputDevice()
  }, 1000);
  // });
}
navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
let debouncedStartTranslation;
onMounted(async () => {
  const openThirdlyLanguageStatusInfo = localStorage.getItem('openThirdlyLanguageStatus')
  if(openThirdlyLanguageStatusInfo){
      openThirdlyLanguageStatus.value = openThirdlyLanguageStatusInfo === 'true'
  }
  console.log('openThirdlyLanguageStatus ',openThirdlyLanguageStatus.value)
  //asr点击函数
  debouncedStartTranslation = debounce(startTranslation, 500, true); // 立即执行    
  connectToBoothWebsocktServer()
  microphoneDetect.value = new DetectFun()
  ipcRenderer.on('soxerror', (event, arg) => {
    console.log('收到来自主进程的消息:', arg);
    errorRecord(arg).then(res=>{console.log(res)})
  });
  ipcRenderer.on("closeRealtimeTranslateWindow",(event, message) => {
    isRealtimeTranslateOpen.value = false
  })
  ipcRenderer.on('winLiveCaption-captionUpdate', (event,captionData) => {
    dealWinLcAsrData(captionData)
  })

  //检查是否过了试用期
  checkInterval.value = setInterval(() => {
    checkTerpMetaTrial().then(res => {
      console.log('checkTerpMetaTrial ', res)
    })
  }, 1000 * 60 * 10);
  // 加载用户设置
  loadUserSettingInfo()
  await setAudioInputDevice()
  // 监听ASR识别结果的回调函数
  ipcRenderer.on('asrResult', (e, asrResult) => handleAsrResult(e, asrResult));
  //加载用户选择的语言信息
  loadLanguageInfo()
  // 展示历史存储的翻译结果 开始
  loadHistoryInfo()
  // 加载场景 开始
  loadSceneInfo()
  window.addEventListener("beforeunload", (e) => beforeunloadHandler(e));
  getAllGlossarySceneData()
});

const getAllGlossarySceneData = () => {
  getAllGlossaryScene({}).then(res => {
    allGlossarySceneData.value = [...res.data]
  })
}
onBeforeUnmount(() => {
  clearInterval(checkInterval.value)
  stopAsr()
  navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
  window.removeEventListener("beforeunload", (e) =>
    beforeunloadHandler(e)
  );
})
/**
 * booth ws测试
 */
const connectToBoothWebsocktServer = async () => {
  const userId = JSON.parse(localStorage.getItem('userData')).userId
  boothWebsocketClient.on('connected', () => {
    boothWebsocketClient.sendInit(userId)
    boothWebsocketClient.startHeartbeat(userId)
  });
  boothWebsocketClient.on('disconnected', () => {

  });
  boothWebsocketClient.on('message', (message) => {
    console.log(message)
    if (message.type == 'glossary-change') {
      let flag = false
      const sceneId = message.sceneId
      console.log('glossary-change ', allGlossarySceneData.value)
      allGlossarySceneData.value.forEach(item => {
        //判断item的id是否在选中的里面，然后判断item的sourceI有没有，是不是在通知里面
        if (item.sourceSceneId == sceneId) {
          flag = true
        }
      })
      if (glossarySceneId.value.includes(sceneId)) {
        console.log('contain sceneId ', sceneId, ' glossarySceneId ', glossarySceneId.value)
        flag = true
      }
      if (flag) {
        updateGlossarySceneInfo()
      }
    }
  });
  await boothWebsocketClient.connect()
}
const loadLanguageInfo = () => {
  // 加载用户上次的语言设置
  console.log('openThirdlyLanguageStatus ',openThirdlyLanguageStatus.value)
  const langListHistory = localStorage.getItem('langList')
  if (langListHistory) {
    console.log('has history lang list')
    langList.value = JSON.parse(langListHistory)
    // 初始化要展示的语言为 主语言/次语言
    langList.value = langList.value.map(item => {
      item.isShow = false
      if (item.main) {
        item.isShow = true
      }
      return item
    })
    lang.value = langList.value.find(item => item.isShow)
  } else {
    console.log('no history lang list')
    langList.value = [
      { code: 'en-US', show: 'English-United States', isShow: true, main: true },
      { code: 'zh-CN', show: 'Chinese-Mandarin', isShow: false, second: true }
    ]
    lang.value = {
      code: 'en-US',
      show: 'English-United States',
      isShow: true,
      main: true
    }
    localStorage.setItem('langList', JSON.stringify(langList.value))
    localStorage.setItem('lang', JSON.stringify(lang.value))
  }
}
//加载存储的历史记录
const loadHistoryInfo = () => {
  try {
    const translationStorage = JSON.parse(localStorage.getItem('translationRes'))
    if (translationStorage && translationStorage.length) {
      // 初始化结果id为上一个正常数据的id + 1
      translationId.value = translationStorage[translationStorage.length - 1].result[translationStorage[translationStorage.length - 1].result.length - 1].id + 1 || 0
      translationRes.value = translationStorage
    }
  } catch (e) {

  }
}
//加载场景
const loadSceneInfo = async () => {
  const glossarySceneIdStr = localStorage.getItem('glossarySceneId');
  if (glossarySceneIdStr) {
    const glossarySceneIdArray = glossarySceneIdStr.split(',');
    const glossarySceneIdArr = glossarySceneIdArray.map(Number);
    glossarySceneId.value = glossarySceneIdArr
    const glossary = await getGlossaryByIds({
      startPage: 0,
      pageNum: 9999999,
      searchInfo: '',
      ids: glossarySceneId.value
    })
    glossaryRes.value = glossary.data.data
  }
  if (localStorage.getItem('replaceSceneId')) {
    replaceSceneId.value = parseInt(localStorage.getItem('replaceSceneId'))
    const replace = await getReplace({
      startPage: 0,
      pageNum: 9999999,
      searchInfo: '',
      sceneId: replaceSceneId.value
    })
    replaceRes.value = replace.data.data
  }
}
//加载用户设置
const loadUserSettingInfo = async () => {
  try {
    const settingRes = await getSetting(JSON.parse(localStorage.getItem('userData')).userId)
    if (settingRes.success) {
      theme.value = settingRes.data.theme === 'true'
      color.value = settingRes.data.glossaryHighlightColor || '#FF4500'
      fontSize.value = settingRes.data.fontSize.toString() || '28'
      store.commit('setTheme', theme.value)
      store.commit('setColor', color.value)
      store.commit('setFontSize', fontSize.value)
      if (!theme.value) {
        document.querySelector('html').classList.add('dark')
      } else {
        document.querySelector('html').classList.remove('dark')
      }
      localStorage.setItem('settingId', settingRes.data.id)
    }else{
      console.log('no user setting info')
    }
  } catch (err) {
    theme.value = true
    if (!theme.value) {
      document.querySelector('html').classList.add('dark')
    } else {
      document.querySelector('html').classList.remove('dark')
    }
    console.log('theme ',theme.value)
  }
}
// ASR识别结果处理
const handleAsrResult = (e, { type, data }) => {
  // console.log('asr result data ',data,' isStart ',isStart.value)
  // const data = {"speaker":speaker, "result": text, "type": "speechRecognized" }
  if (!isStart.value) return
  const id = translationId.value
  let {speaker, result} = JSON.parse(data)
  if(!speaker){
    speaker = ''
  }
  const interim_transcript = speaker + result
  const translationData = {
    transcript: interim_transcript,
    id: id
  }
  translationData.original = interim_transcript
  translationData.replace = interim_transcript
  translationData.glossary = interim_transcript
  translationData.isGlossary = false
  translationData.isReplace = false
  const replaceTranscriptData = switchTranslation(translationData, replaceRes.value, 'replace', lang.value)
  const glossaryTranscriptData = switchTranslation(replaceTranscriptData, glossaryRes.value, 'glossary', lang.value)
  // 替换结果集中此索引的结果元素
  const index = translationRes.value[translationRes.value.length - 1].result?.findIndex(item => item.id === glossaryTranscriptData.id);
  if (index !== -1) {
    translationRes.value[translationRes.value.length - 1].result[index] = glossaryTranscriptData
  } else {
    translationRes.value[translationRes.value.length - 1].result.push(glossaryTranscriptData)
  }
  // console.log('asr result data --- ',data)
  // 当翻译完这句话后，id加一
  if (JSON.parse(data).type === 'speechRecognized') {
    translationId.value++
  }
  let sendData = {asrData:JSON.parse(data),lang:lang.value,langList:langList.value,openConsecutive:openConsecutive.value}
  ipcRenderer.send('speaker-hide-status-change', JSON.stringify(speakerArr.value))
  ipcRenderer.send('realtime-translate-data', JSON.stringify(sendData));
}
// 设置组件需要调用的方法 结束
const beforeunloadHandler = () => {
  localStorage.setItem('translationRes', JSON.stringify(translationRes.value))
}
// 拖拽文件进来窗口触发的操作
const handleDragEnter = event => {
  event.preventDefault();
  uploading.value = true
}
// 拖拽文件移动的操作
const handleDragOver = event => {
  event.preventDefault();
}

//开启asr 按钮点击
const startTranslation = async () => {
  startAsr()
}
const stopAsr = async (stopSox = true) => {
  if (asrType.value == 2) {
    clearInterval(refreshAzureAsrTokenInterval.value)
    console.log('stop online asr')
    if(azureRecognizer.value) azureRecognizer.value.stopContinuousRecognitionAsync()
    if(transcriberRecognizer.value) {
      speakerArr.value = []
      transcriberRecognizer.value.stopTranscribingAsync()
    }
  }
  if (asrType.value == 3) {
    startAsrLoading.value = true
    console.log('stop xunfei asr')
    clearAudioDataAndStop()
    if (xunfeiInterval.value) {
      clearInterval(xunfeiInterval.value)
    }
    await xunfeiClient.disconnect()
  }
  if (asrType.value == 4) {
    const result = await ipcRenderer.invoke('winLiveCaption-stopWatchCaption')
    if (result.success) {
      console.log('字幕监控已停止:', result.data)
    }
    await ipcRenderer.invoke('winLiveCaption-stop')
  }
  let info = {
    device: microphone.value.name,
    language: lang.value.code,
    stopSox
  }
  console.log(info)
  inputDeviceVolume.value = 0
  microphoneDetect.value && microphoneDetect.value.closeDetect()
  ipcRenderer.invoke('asrStatus', {asrStatus: isStart.value, stopSox})
  await ipcRenderer.invoke('stopAsr', info)
}
const getActiveMicDeviceIdByName = async (deviceName) => {
  const devices = await navigator.mediaDevices.enumerateDevices();
  let inputDevices = devices.filter(device => device.kind === 'audioinput')
  let selectDeviceId = 'defalut'
  let selectDeviceName = 'default'
  inputDevices.forEach(item => {
    const name = item.label
    const deviceId = item.deviceId
    console.log(item)
    if (!name.includes('UI Sounds')) {
      if (deviceName.includes(name)) {
        selectDeviceName = name
        selectDeviceId = deviceId
      }
      if (name.includes(deviceName)) {
        selectDeviceName = name
        selectDeviceId = deviceId
      }
    }
  })
  return selectDeviceId
}
//输入音频音量检测
const audioInputVolumeDetected = async (deviceName) => {
  let selectDeviceId = await getActiveMicDeviceIdByName(deviceName)
  let activeMic = { audio: { deviceId: { exact: selectDeviceId } } }
  microphoneDetect.value.beginDetect((res) => {
    if (res.code == 200) {
      let volume = Math.ceil(res.num / (100 / 17))
      const randomIncrement = Math.floor(Math.random() * 11) + 20; // 生成 10 - 20 的随机数
      //   console.log('inputDeviceVolume----- ',volume)
      inputDeviceVolume.value = volume == 0 ? 0 : volume * 5
      //   console.log('inputDeviceVolume+++++ ',inputDeviceVolume.value)
    }
  }, activeMic)
  console.log('select device name deviceId ', selectDeviceId)
}
const audioInputVolumeDetectedByDeviceId = async (selectDeviceId) => {
  let activeMic = { audio: { deviceId: { exact: selectDeviceId } } }
  microphoneDetect.value.beginDetect((res) => {
    if (res.code == 200) {
      let volume = Math.ceil(res.num / (100 / 17))
      const randomIncrement = Math.floor(Math.random() * 11) + 20; // 生成 10 - 20 的随机数
      //   console.log('inputDeviceVolume----- ',volume)
      inputDeviceVolume.value = volume == 0 ? 0 : volume * 5
      //   console.log('inputDeviceVolume+++++ ',inputDeviceVolume.value)
    }
  }, activeMic)
  console.log('select device name deviceId ', selectDeviceId)
}

/**
 * 讯飞asr实现
 */
//讯飞在线识别
let resultText = "";
let resultTextTemp = "";
let lastSentText = ""; // 记录上次发送的完整文本，用于去重
const useXunfeiOnlineAsr = async () => {
  resultText = ''
  resultTextTemp = ''
  lastSentText = '' // 重置去重记录
  xunfeiClient.off('connected', handleConnection);
  xunfeiClient.off('disconnected', handleDisconnection);
  xunfeiClient.off('message', handleMessage);
  xunfeiClient.off('error', handleError);
  xunfeiClient.on('connected', handleConnection);
  xunfeiClient.on('disconnected', handleDisconnection);
  xunfeiClient.on('message', handleMessage);
  xunfeiClient.on('error', handleError);
  const language = asrDeviceInfo.value.language
  console.log('language ', language)
  let lang = 'cn'
  if (language.includes('en-') || language.includes('english')) {
    lang = 'en'
  }
  await xunfeiClient.connect(lang)
  // WebSocket connected
}
// WebSocket event handlers
const stream = ref(null)
const source = ref(null)
const processor = ref(null)
const ringBuffer = ref(null)
const xunfeiIsProcessing = ref(true)
const handleConnection = async () => {
  console.log('handleConnection');
  stopAsrLoading.value = false
  try {
    let deviceName = asrDeviceInfo.value.device;
    console.log('deviceName', deviceName);

    if (deviceName === 'System Audio') {
      if (Platform.is.win) {
        deviceName = 'GTEAudio';
      } else {
        deviceName = 'GTEAudio 2ch';
      }
      let list = await navigator.mediaDevices.enumerateDevices()
      const hasGTE = list.find(item => {
        return item.label.includes('GTEAudio')
      })
      if(!hasGTE){
        showInstallGTEAudio.value = true
        return
      }
      let res = await ipcRenderer.invoke('startSystemAudioModeChange', null);
      console.log('startSystemAudioModeChange ',res)
    }

    let audioDeviceId = await getActiveMicDeviceIdByName(deviceName);
    audioInputVolumeDetectedByDeviceId(audioDeviceId);
    console.log('audioDeviceId', audioDeviceId);

    stream.value = await navigator.mediaDevices.getUserMedia({ audio: { deviceId: { exact: audioDeviceId } }, video: false });
    console.log('Microphone access granted');

    const audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 });
    source.value = audioContext.createMediaStreamSource(stream.value);
    processor.value = audioContext.createScriptProcessor(0, 1, 1);
    source.value.connect(processor.value);
    processor.value.connect(audioContext.destination);

    // RingBuffer
    class RingBuffer {
      constructor(size) {
        this.buffer = new Int16Array(size);
        this.size = size;
        this.readPtr = 0;
        this.writePtr = 0;
        this.length = 0;
      }

      write(data) {
        for (let i = 0; i < data.length; i++) {
          this.buffer[this.writePtr] = data[i];
          this.writePtr = (this.writePtr + 1) % this.size;
          if (this.length < this.size) {
            this.length++;
          } else {
            this.readPtr = (this.readPtr + 1) % this.size;
          }
        }
      }

      read(count) {
        if (this.length < count) return null;
        const result = new Int16Array(count);
        for (let i = 0; i < count; i++) {
          result[i] = this.buffer[this.readPtr];
          this.readPtr = (this.readPtr + 1) % this.size;
        }
        this.length -= count;
        return result;
      }
    }

    ringBuffer.value = new RingBuffer(32000);
    xunfeiIsProcessing.value = true
    // 音频处理
    processor.value.onaudioprocess = (e) => {
      const input = e.inputBuffer.getChannelData(0);
      const int16Data = floatTo16BitPCM(input);
      ringBuffer.value.write(int16Data);

      // 尝试尽可能多发送
      while (xunfeiIsProcessing.value) {
        const chunk = ringBuffer.value.read(640);
        if (!chunk) break;
        console.log('sending chunk, size:', chunk.length);
        const byteBuffer = new Int8Array(chunk.buffer);
        try {
          xunfeiClient.sendBinary(byteBuffer);
        } catch (e) {
          console.error('xunfei e ', e);
          if (e.message && e.message.includes('WebSocket is not connected')) {
            clearAudioDataAndStop();
          }
        }
      }
    };

    function floatTo16BitPCM(input) {
      const output = new Int16Array(input.length);
      for (let i = 0; i < input.length; i++) {
        const s = Math.max(-1, Math.min(1, input[i]));
        output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
      }
      return output;
    }

  } catch (err) {
    console.error('Error accessing microphone:', err);
  }
};
const clearAudioDataAndStop = () => {
    return new Promise((resolve, reject) => {
        try {
            xunfeiIsProcessing.value = false;
            if (stream.value) {
                stream.value.getTracks().forEach(track => track.stop());
            }
            if (source.value) {
                source.value.disconnect();
            }
            if (processor.value) {
                processor.value.onaudioprocess = null;
                processor.value.disconnect();
            }
            if (ringBuffer.value) {
                ringBuffer.value.buffer = new Int16Array(ringBuffer.value.size);
                ringBuffer.value.readPtr = 0;
                ringBuffer.value.writePtr = 0;
                ringBuffer.value.length = 0;
            }
            resolve();
        } catch (error) {
            console.error('Error during audio cleanup:', error);
            reject(error);
        }
    });
};

// 中英文智能断句函数
const isChinese = (char) => /[\u4e00-\u9fff]/.test(char);
const isEnglish = (char) => /[a-zA-Z]/.test(char);
const isPunctuation = (char) => /[.,!?;:，。！？；：]/.test(char);
const getCharType = (char) => {
  if (isChinese(char)) return 'chinese';
  if (isEnglish(char)) return 'english';
  if (isPunctuation(char)) return 'punctuation';
  if (/\s/.test(char)) return 'whitespace';
  return 'other';
};

// 检查文本是否与上次发送的内容重复
const isDuplicateText = (newText, lastText) => {
  if (!lastText || !newText) return false;

  // 简单的重复检查：如果新文本包含在上次文本中，或者相似度很高
  if (lastText.includes(newText) || newText.includes(lastText)) {
    return true;
  }

  // 检查英文部分的重复（针对英文识别不稳定的问题）
  const englishRegex = /[a-zA-Z\s.,!?;:]+/g;
  const newEnglish = newText.match(englishRegex)?.join('').trim() || '';
  const lastEnglish = lastText.match(englishRegex)?.join('').trim() || '';

  if (newEnglish && lastEnglish && newEnglish.length > 10) {
    // 计算英文部分的相似度
    const similarity = calculateSimilarity(newEnglish, lastEnglish);
    if (similarity > 0.8) { // 80%以上相似度认为是重复
      return true;
    }
  }

  return false;
};

// 简单的文本相似度计算
const calculateSimilarity = (str1, str2) => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = getEditDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
};

// 计算编辑距离
const getEditDistance = (str1, str2) => {
  const matrix = [];
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  return matrix[str2.length][str1.length];
};

const getFirstCompletedSegment = (text) => {
  if (!text || text.trim() === '') {
    return { completedText: '', remainingText: '' };
  }

  // 简化的断句阈值
  const MIN_CHINESE_LENGTH = 10; // 中文字符数阈值
  const MIN_ENGLISH_WORDS = 10;   // 英文单词数阈值

  let currentSegment = '';
  let lastCharType = null;

  // 计算中文字符数量
  const countChineseChars = (str) => {
    const chineseChars = str.match(/[\u4e00-\u9fff]/g);
    return chineseChars ? chineseChars.length : 0;
  };

  // 计算英文单词数量
  const countEnglishWords = (str) => {
    const englishText = str.match(/[a-zA-Z\s]+/g);
    if (!englishText) return 0;
    return englishText.join(' ').trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const charType = getCharType(char);

    if (lastCharType === null) {
      currentSegment += char;
      lastCharType = charType;
      continue;
    }

    let shouldBreak = false;

    if (charType !== 'whitespace') {
      // 中文到英文的切换
      if (lastCharType === 'chinese' && charType === 'english') {
        // 检查后续英文部分是否达到阈值
        const remainingText = text.slice(i);
        const englishWords = countEnglishWords(remainingText);
        if (englishWords >= MIN_ENGLISH_WORDS) {
          shouldBreak = true;
        }
      }
      // 英文到中文的切换
      else if (lastCharType === 'english' && charType === 'chinese') {
        // 检查后续中文部分是否达到阈值
        const remainingText = text.slice(i);
        const chineseCount = countChineseChars(remainingText);
        if (chineseCount >= MIN_CHINESE_LENGTH) {
          shouldBreak = true;
        }
      }
      // 标点符号后的语言切换
      else if (lastCharType === 'punctuation') {
        // 查看标点符号前的字符类型
        let prevNonPunctuationType = null;
        for (let j = i - 1; j >= 0; j--) {
          const prevCharType = getCharType(text[j]);
          if (prevCharType !== 'punctuation' && prevCharType !== 'whitespace') {
            prevNonPunctuationType = prevCharType;
            break;
          }
        }
        if (prevNonPunctuationType && prevNonPunctuationType !== charType) {
          // 检查后续新语言的长度
          const remainingText = text.slice(i);
          if (prevNonPunctuationType === 'chinese' && charType === 'english') {
            // 前面是中文，后面是英文，检查后续英文长度
            const englishWords = countEnglishWords(remainingText);
            if (englishWords >= MIN_ENGLISH_WORDS) {
              shouldBreak = true;
            }
          } else if (prevNonPunctuationType === 'english' && charType === 'chinese') {
            // 前面是英文，后面是中文，检查后续中文长度
            const chineseCount = countChineseChars(remainingText);
            if (chineseCount >= MIN_CHINESE_LENGTH) {
              shouldBreak = true;
            }
          }
        }
      }
    }

    if (shouldBreak) {
      const trimmedSegment = currentSegment.trim();
      const remainingText = text.slice(i).trim();

      // 简单检查：确保有基本内容
      if (trimmedSegment.length >= 5) {
        return { completedText: trimmedSegment, remainingText: remainingText };
      }
    }

    currentSegment += char;

    if (charType !== 'whitespace') {
      lastCharType = charType;
    }
  }

  // 没有找到合适的断句点，返回空的完成文本
  return { completedText: '', remainingText: text };
};

const handleDisconnection = () => {
  startAsrLoading.value = false
  console.log('xunfei WebSocket disconnected');
};

const handleMessage = async (message) => {
  console.log('xunfei message ',message.action)
  if(message.action && message.action == 'error'){
    //讯飞没钱了
    await clearAudioDataAndStop()
    if (xunfeiInterval.value) {
      clearInterval(xunfeiInterval.value)
    }
    await xunfeiClient.disconnect()
    isStart.value = false
    stopAsrLoading.value = false
    ElMessage({
        message: 'The Online-ZH/EN engine is down. Please try other engines first.',
        grouping: true,
        type: 'warning',
    })
    return
  }
  const data = JSON.parse(message.data)
  console.log('xunfei message ',data)
  // 转写结果
  let resultTextTemp = ""
  data.cn.st.rt.forEach((j) => {
    j.ws.forEach((k) => {
      k.cw.forEach((l) => {
        resultTextTemp += l.w;
      });
    });
  });
  if (data.cn.st.type == 0) {
    //最终】识别结果：
    resultText += resultTextTemp;
    resultTextTemp = ""
  }
  //讯飞asr结果，要进行断句处理
  let text = resultText + resultTextTemp
  console.log('总的asr结果：',text)

  // 使用中英文智能断句 - 优先使用语言边界断句
  const { completedText, remainingText } = getFirstCompletedSegment(text)

  if (completedText) {
    // 检查是否与上次发送的内容重复
    if (!isDuplicateText(completedText, lastSentText)) {
      // 有完整的语言段落，且不重复，发送speechRecognized
      // 更精确地更新resultText：确保完全移除已处理的部分
      const completedLength = completedText.length;
      let updatedResultText = '';

      // 从原始resultText中精确移除已处理的部分
      if (resultText.length > 0) {
        // 如果completedText完全来自resultText
        if (resultText.includes(completedText)) {
          const index = resultText.indexOf(completedText);
          updatedResultText = resultText.slice(index + completedLength).trim();
        } else {
          // 如果completedText跨越了resultText和resultTextTemp
          updatedResultText = remainingText;
        }
      } else {
        updatedResultText = remainingText;
      }

      resultText = updatedResultText;
      lastSentText = completedText; // 记录本次发送的内容

      console.log('xunfei speechRecognized (智能断句)：', completedText)
      console.log('更新后的resultText：', resultText)

      const data = {"source":"xunfei", "result": completedText, "type": "speechRecognized" }
      handleAsrResult(null, { type: 'speechRecognized', data: JSON.stringify(data) })

      // 如果还有剩余文本，发送speechRecognizing
      if (remainingText.trim()) {
        console.log('xunfei speechRecognizing (剩余文本)：', remainingText)
        const remainingData = {"source":"xunfei", "result": remainingText, "type": "speechRecognizing" }
        handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(remainingData) })
      }
    } else {
      console.log('检测到重复内容，跳过发送：', completedText)
      // 即使跳过发送，也要更新resultText以避免累积
      resultText = remainingText;

      // 发送剩余文本作为speechRecognizing
      if (remainingText.trim()) {
        console.log('xunfei speechRecognizing (去重后剩余)：', remainingText)
        const remainingData = {"source":"xunfei", "result": remainingText, "type": "speechRecognizing" }
        handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(remainingData) })
      }
    }
  } else {
    // 没有完整段落，使用原有的标点符号断句逻辑作为备选
    let textBeforeFifthPunctuation = getTextBeforeFifthPunctuation(text)
    if(textBeforeFifthPunctuation){
      // 找到了需要断句的文本，直接从resultText中移除已处理的部分
      resultText = resultText.slice(textBeforeFifthPunctuation.length)
      console.log('xunfei speechRecognized (标点断句)：',textBeforeFifthPunctuation)
      const data = {"source":"xunfei", "result": textBeforeFifthPunctuation, "type": "speechRecognized" }
      handleAsrResult(null, { type: 'speechRecognized', data: JSON.stringify(data) })
    }else{
      console.log('xunfei speechRecognizing：',text)
      const data = {"source":"xunfei", "result": text, "type": "speechRecognizing" }
      handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(data) })
    }
  }
};
const handleError = (error) => {
  console.error('WebSocket error:', error);
};
const getNowUseGlossaryInfo = ()=>{
    const allPhraseFlatMap = glossaryRes.value.flatMap(item => [
      item.glossaryA, 
      item.glossaryB
    ]);
    return allPhraseFlatMap;
}
const restartMicrosoftOnlineAsr = ()=>{
  if (asrType.value == 2 && isStart.value ) {
      //微软语音识别时，如果修改词汇表，如果正在asr，要重启
      if(azureRecognizer.value) azureRecognizer.value.stopContinuousRecognitionAsync()
      if(transcriberRecognizer.value) {
        speakerArr.value = []
        transcriberRecognizer.value.stopTranscribingAsync()
      }
      console.log('watch restart asr ',asrDeviceInfo.value)
      useMicrosoftOnlineAsr(asrDeviceInfo.value)
  }
}
const speakerStatusChange = (speaker) =>{
  speakerArr.value = speakerArr.value.map(item => {
    if (item.name === speaker.name) {
      item.hide = speaker.hide
    }
    return item
  })
}
const hideSpeaker = (speakerName) => {
  speakerArr.value = speakerArr.value.map(item => {
    if (item.name === speakerName) {
      item.hide = true
    }
    return item
  })
}
const startingAzureAsr = ref(false)
const refreshAzureAsrTokenInterval = ref(null)
//微软在线识别
const useMicrosoftOnlineAsr = async (info) => {
  if(azureRecognizer.value) azureRecognizer.value.stopContinuousRecognitionAsync()
  speakerArr.value = []
  if(transcriberRecognizer.value) {
      transcriberRecognizer.value.stopTranscribingAsync()
  }
  let token = "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.h_plFV3XfmJG2Czo_KjTuM_h3lcMerz6OJa8B7dLXC-ltrpVvb9Bb37YhCsfnNID-APjQT5HAxz07KkpNaY-5A"
  let _resp = await getAzureAsrToken()
  if(_resp){
    token = _resp.token
  }
  let languageRegionArr = []
  if(lang.value.code === 'Auto'){
    //第三语言，自动识别
    langList.value.forEach(item=>{
      if(item.code != 'Auto'){
        languageRegionArr.push(item.code)
      }
    })
  }else{
    languageRegionArr.push(lang.value.code)
  }
  console.log('languageRegionArr ',languageRegionArr)
  var recognizer = null
  // const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(azureSubscriptionKey.value, azureRegion.value);
  const speechConfig = SpeechSDK.SpeechConfig.fromAuthorizationToken(token, azureRegion.value);
  const autoDetectSourceLanguageConfig = SpeechSDK.AutoDetectSourceLanguageConfig.fromLanguages(languageRegionArr);

  //设置单一语言
  //languageRegion.value = info.language
  //speechConfig.speechRecognitionLanguage = languageRegion.value;
  //不过滤脏话，展示原始文本
  speechConfig.setProfanity(SpeechSDK.ProfanityOption.Raw);
  speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_LanguageIdMode, "Continuous")
  console.log('start azure asr language region ', languageRegion.value, ' device name ', info.device)
  // let audioDeviceId = this.activeMic.audio.deviceId.exact
  if (info.device === 'System Audio') {
    if (Platform.is.win) {
      info.device = 'GTEAudio'
    } else {
      info.device = 'GTEAudio 2ch'
    }
    let list = await navigator.mediaDevices.enumerateDevices()
    const hasGTE = list.find(item => {
      return item.label.includes('GTEAudio')
    })
    if(!hasGTE){
     showInstallGTEAudio.value = true
     return
    }
    //需要进行SOX命令
    let res = await ipcRenderer.invoke('startSystemAudioModeChange', null);
    console.log('startSystemAudioModeChange ',res)
  }
  let audioDeviceId = await getActiveMicDeviceIdByName(info.device)
  audioInputVolumeDetectedByDeviceId(audioDeviceId)
  console.log('audioDeviceId ', audioDeviceId)
  const audioConfig = SpeechSDK.AudioConfig.fromMicrophoneInput(audioDeviceId);
  console.log('audioConfig ', audioConfig)
  // recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);
  recognizer = SpeechSDK.SpeechRecognizer.FromConfig(speechConfig, autoDetectSourceLanguageConfig, audioConfig);

  const transcriber = SpeechSDK.ConversationTranscriber.FromConfig(speechConfig, autoDetectSourceLanguageConfig, audioConfig);

  refreshAzureAsrTokenInterval.value = setInterval(async () => {
    try{
      const _response = await getAzureAsrToken()
      console.log('refresh azure token ',_response.token)
      recognizer.authorizationToken = _response.token
    }catch(e){}
  }, 5 * 60 * 1000);
  var phraseListGrammar = SpeechSDK.PhraseListGrammar.fromRecognizer(recognizer);
  azureRecognizer.value = recognizer
  //微软添加短语列表
  let phraseList = getNowUseGlossaryInfo()
  phraseList.forEach(item=>{
    // phraseListGrammar.addPhrases(item);
  })
  console.log('recognizer ', recognizer)

  transcriber.transcribed = (s, e) => {
    let text = e.result.text
    if(!text) return
    console.log('transcribed ',e)
    let languageCode = e.privResult.privLanguage
    console.log(`Speaker: ${e.result.speakerId}, Text: ${e.result.text}`);
    let speaker = mapSpeaseker(e.result.speakerId)
    //设置所有说话人
    if (!speakerArr.value.find(item => item.name === speaker)) {
      const info = { name: speaker, hide: false }
      console.log('info ',info)
      speakerArr.value = [...speakerArr.value, info]
    }
    const data = {"source":"azure","speaker":speaker, "result": text,"languageCode":languageCode, "type": "speechRecognized" }
    handleAsrResult(e, { type: 'speechRecognized', data: JSON.stringify(data) })
  };

  transcriber.transcribing = (s, e) => {
    console.log('transcribing ',e)
    console.log(`Speaker: ${e.result.speakerId}, Text: ${e.result.text}`);
    let languageCode = e.privResult.privLanguage
    const data = {"source":"azure", "result": e.result.text,"languageCode":languageCode, "type": "speechRecognizing" }
    handleAsrResult(e, { type: 'speechRecognizing', data: JSON.stringify(data) })
  };
  transcriber.sessionStarted = (s, e) => {
    console.log("Session started");
    stopAsrLoading.value = false
  };

  transcriber.canceled = (s, e) =>{
    console.log('canceled: ',e)
    isStart.value = false
    stopAsrLoading.value = false
    ElMessage({
        message: 'The Online1 engine is down. Please try other engines first.',
        grouping: true,
        type: 'warning',
    })
  }
  
  recognizer.recognized = (s, e) => {
    const text = e.result.text
    // console.log(languageRegion.value + 'RECOGNIZED: ', e.privSessionId);
    // console.log(languageRegion.value + `RECOGNIZED: Text=${e.result.text}`);
    //{"result":"如果人","speakingRate":0,"type":"speechRecognized"}
    const data = { "result": text, "type": "speechRecognized" }
    handleAsrResult(e, { type: 'speechRecognized', data: JSON.stringify(data) })
  };
  recognizer.recognizing = (s, e) => {
    const text = e.result.text
    // console.log("RECOGNIZING: " + e.result.text);
    const data = { "result": text, "type": "speechRecognizing" }
    handleAsrResult(e, { type: 'speechRecognizing', data: JSON.stringify(data) })
    //{"result":"如果人","speakingRate":0,"type":"speechRecognizing"}
  };
  recognizer.sessionStarted = (s, e) => {
    console.log("sessionStarted: ", e.sessionId);
    stopAsrLoading.value = false
  };
  recognizer.canceled = (s, e) =>{
    console.log('canceled: ',e)
    isStart.value = false
    stopAsrLoading.value = false
    ElMessage({
        message: 'The Online1 engine is down. Please try other engines first.',
        grouping: true,
        type: 'warning',
    })
  }
  // if(openConsecutive.value){
  //   transcriber.startTranscribingAsync();
  //   transcriberRecognizer.value = transcriber
  // }else{
  //   recognizer.startContinuousRecognitionAsync();
  // }
  //微软 都要使用说话人识别
  transcriber.startTranscribingAsync();
  transcriberRecognizer.value = transcriber
}
const findClosestLanguage = (data,input)=> {
  const entries = Object.entries(data.translations);
  // 优先尝试包含匹配（不区分大小写）
  const exactMatch = entries.find(([key, value]) =>
    value.toLowerCase().includes(input.toLowerCase())
  );
  if (exactMatch) return exactMatch[0];
  // 否则使用 Levenshtein 距离匹配最近的项（需安装 'fastest-levenshtein'）
  const { distance } = require("fastest-levenshtein");
  let minDist = Infinity;
  let bestMatch = null;
  for (const [key, value] of entries) {
    const dist = distance(input.toLowerCase(), value.toLowerCase());
    if (dist < minDist) {
      minDist = dist;
      bestMatch = key;
    }
  }

  return bestMatch;
}
const dealWinLcAsrData = (data) => {
  // 使用ASR处理器处理Windows Live Caption数据
  asrProcessor.processWinLcAsrData(data, handleAsrResult)
}
const startLiveCaptionWatch = async (langListObj)=>{
        const languageName = findClosestLanguage(langListObj,lang.value.show.split('-')[0])
        console.log('languageName ',languageName)
        //3开始切换语言
        const result = await ipcRenderer.invoke('winLiveCaption-setLanguage', languageName)
        if (result.success) {
          //4 开始获取识别结果
          const result = await ipcRenderer.invoke('winLiveCaption-startWatchCaption')
          if (result.success) {
            console.log('字幕监控已启动:', result.data)
          }
          stopAsrLoading.value = false
        } else {
            isStart.value = false
            stopAsrLoading.value = false
        }
}
//使用Windows live caption
const useWinLiveCaption = async (info) =>{
  //1 开启lc
  const start_lc = await ipcRenderer.invoke('winLiveCaption-start')
   console.log('## start_lc')
   asrProcessor.resetWinLcState()
  if(start_lc && start_lc.success){
    //隐藏窗口
    await ipcRenderer.invoke('winLiveCaption-hide')
    console.log('## start win lc success ',start_lc)
    //2 获取语言列表以及翻译后的内容，如果localstorage有，从localstorage获取，否则通过ipc调用winLiveCaption-getLanguageList获取，存入localstorage
    const langList = localStorage.getItem('win_lc_lang_list')
    if(langList){
      console.log('langList ',langList)
      const langListObj = JSON.parse(langList)
      if(langListObj){
        startLiveCaptionWatch(langListObj)
      }
    }else{
      const options = {
        apiKey: '3ppwpHjYgB3Wja2mwJar7fUKp73PdYRri7vuIJOXmGFC716rzm7nJQQJ99BGAC1i4TkXJ3w3AAAbACOG1nTl' // 可选的API密钥
      }
      const langList = await ipcRenderer.invoke('winLiveCaption-getLanguageList',options)
      console.log(langList)
      if(langList.success){
        localStorage.setItem('win_lc_lang_list', JSON.stringify(langList.data))
        startLiveCaptionWatch(langList.data)
      }else{
        isStart.value = false
        stopAsrLoading.value = false
      }
      
    }
  }else{
    isStart.value = false
    stopAsrLoading.value = false
  }
}
const startAsr = async (newConversation = true, restartSox = true) => {
  const _languageCode =  lang.value.code
  let asrEngine = settingRef.value.getAsrEngineByLanguage(lang.value.code)
  asrType.value = asrEngine
  let info = {
    device: microphone.value.name,
    language: lang.value.code,
    asrType: asrType.value
  }
  asrDeviceInfo.value = info
  if (newConversation) {
    translationRes.value.push({
      id: moment().format('YYYY-MM-DD HH:mm:ss'),
      result: []
    })
  }
  setTimeout(() => {
    asrResultAutoScroll.value = !openConsecutive.value
  }, 1000);
  //使用微软
  if (asrType.value == 2) {
    useMicrosoftOnlineAsr(info)
    isStart.value = true
    stopAsrLoading.value = true
    return
  }
  //使用讯飞
  if (asrType.value == 3) {
    useXunfeiOnlineAsr(info)
    stopAsrLoading.value = true
    isStart.value = true
    return
  }
  //使用win lc
  if(asrType.value == 4){
    // 重置Windows Live Caption的ASR状态
    asrProcessor.resetWinLcState()
    isStart.value = true
    stopAsrLoading.value = true
    useWinLiveCaption(info)
    return
  }
  console.log('start asr ', info)
  let micName = microphone.value.name
  if (info.device === 'System Audio') {
    //检测GTE是否安装,使用GTE进行回环
    micName = 'GTEAudio'
    let list = await navigator.mediaDevices.enumerateDevices()
    const hasGTE = list.find(item => {
      return item.label.includes('GTEAudio')
    })
    if (hasGTE) {
      //使用GTE进行回环
      ipcRenderer.send('startAsrUseGTfEAudio', { info, restartSox, deviceList: JSON.stringify(list) });
    } else {
      showInstallGTEAudio.value = true
      return
    }
  } else {
    ipcRenderer.send('startAsr', info);
  }
  audioInputVolumeDetected(micName)
  isStart.value = true
  ipcRenderer.invoke('asrStatus', {asrStatus: isStart.value})
 
}
const installGTEAudio = () => {
  //安装GTE   
  ipcRenderer.send('installGTEAudio')
  showInstallGTEAudio.value = false
}
const endTranslation = () => {
  if(isStart.value){
   isStart.value = false;
   stopAsr()
  }
}
const openSetting = () => {
  store.commit('setSettingVisible', true)
}
const openFileTranslate = () => {
  // router.push('/fileTranslate')
  ipcRenderer.send('openTranslateWindow')
}
const openRealtimeTranslate = ()=>{
  ipcRenderer.send('openRealTimeTranslateWindow')
}
const openGlossaryScene = () => {
  glossarySceneVisible.value = true
}
const closeGlossaryScene = () => {
  glossarySceneVisible.value = false
}
const openReplace = () => {
  replaceSceneVisible.value = true
}
const closeReplaceScene = () => {
  replaceSceneVisible.value = false
}
const setAudioInputDevice = async () => {
  const micList = await getAudioInputDevice()
  // console.log('micList ',micList)
  audioInputDeviceList.value = micList.map((item, index) => {
    return {
      name: item,
      value: index
    }
  })

  let list = await navigator.mediaDevices.enumerateDevices()
  let defaultInput = list.find(item => {
    return item.deviceId == 'default' && item.kind === 'audioinput'
  })
  let defaultAudioInput = ''
  if (Platform.is.win) {
    let winAudioInputDevices = await ipcRenderer.invoke('getSystemInputDevices', null)
    winAudioInputDevices.forEach(item => {
      if (item.isDefaultMultimedia) {
        defaultAudioInput = item.fullName
      }
    })
  } else {
    defaultAudioInput = macAudioDevices.getDefaultInputDevice.sync().name
  }

  // defaultInput.label = defaultAudioInput.name
  console.log('defaultAudioInput ', defaultAudioInput)
  console.log('microphone.value ', microphone.value)
  console.log('audioInputDeviceList ', audioInputDeviceList)
  if (microphone.value) {
    //如果原来有，则用原来的
    let flag = false
    audioInputDeviceList.value.forEach(item => {
      if (item.name === microphone.value.name) {
        // microphone.value = item
        flag = true
      }
    })
    console.log('flag ', flag)
    if (flag) return
  }

  audioInputDeviceList.value.forEach(item => {
    if (defaultAudioInput.includes(item.name)) {
      microphone.value = item
    }
  })
}

const getAudioInputDevice = async () => {
  let deviceNames = []
  deviceNames.push('System Audio')
  if (Platform.is.win) {
    let winAudioInputDevices = await ipcRenderer.invoke('getSystemInputDevices', null)
    winAudioInputDevices.forEach(item => {
      if (!item.fullName.includes('(UI Sounds)')) {
        deviceNames.push(item.fullName)
      }
    })
  } else {
    let macAudioInputDevices = macAudioDevices.getInputDevices.sync()
    console.log('macAudioDevices ', macAudioInputDevices)
    macAudioInputDevices.forEach(item => {
      if (!item.name.includes('(UI Sounds)')) {
        deviceNames.push(item.name)
      }
    })
  }
  return [...new Set(deviceNames)]
}
// 切换主语言的方法
const handleMainLanguage = (mainLang) => {
  langList.value = langList.value.map(item => {
    if (item.main) {
      item.code = mainLang.code
      item.show = mainLang.show
    }
    return item
  })
  localStorage.setItem('langList', JSON.stringify(langList.value))
}
// 切换次语言的方法
const handleSecondLanguage = (secondLang) => {
  langList.value = langList.value.map(item => {
    if (item.second) {
      item.code = secondLang.code
      item.show = secondLang.show
    }
    return item
  })
  localStorage.setItem('langList', JSON.stringify(langList.value))
}
//切换main asr引擎
const handleChangeMainAsrEngineParent = async (engine,language) =>{
    stopAsrIfStart()
    localStorage.setItem('mainAsrEngine', engine)
}
//切换second asr引擎
const handleChangeSecondAsrEngineParent = async (engine,language) =>{
    stopAsrIfStart()
    localStorage.setItem('sencondAsrEngine', engine)
}
//切换 auto asr引擎
const handleChangeThirdlyAsrEngineParent = async (engine)=>{
  if(!openThirdlyLanguageStatus.value) {
    //如果没有打开第三语言
    return
  }
  stopAsrIfStart()
  localStorage.setItem('thirdlyAsrEngine',engine)
}
//是否使用 auto语言
const changeOpenThirdlyLanguage = ()=>{
  openThirdlyLanguageStatus.value = !openThirdlyLanguageStatus.value
  localStorage.setItem('openThirdlyLanguageStatus',openThirdlyLanguageStatus.value)
  if(openThirdlyLanguageStatus.value){
    //使用第三语言
    if(langList.value.length == 2){
      langList.value.push({code: 'Auto', show: 'Auto', isShow: false, third: true})
    }
  }else{
    // 检查当前选中的语言是否是Auto
    const isCurrentlyAuto = lang.value && lang.value.code === 'Auto';

    if(langList.value.length == 3){
      if(langList.value[2].code == 'Auto'){
        langList.value.pop();
      }
    }

    // 如果当前选中的是Auto，需要切换到第一个语言
    if(isCurrentlyAuto) {
      selectFirstLang()
      // 更新localStorage
      localStorage.setItem('langList', JSON.stringify(langList.value))
      localStorage.setItem('lang', JSON.stringify(lang.value))
    }
  }
  console.log('langList.value ',langList.value)
}
const stopAsrIfStart = async ()=>{
    if (isStart.value) {
        await stopAsr()
        isStart.value = false;
    }
}
//切换Asr方式
const handleAsrPatternChange = async (asrPattern) => {
  // {code: 1, show: 'Offline'}
  if (isStart.value) {
    await stopAsr()
    isStart.value = false;
  }
  asrType.value = asrPattern.code
}
//选中第一个元素
const selectFirstLang = () => {
  if (langList.value.length === 0) return

  langList.value = langList.value.map((item, index) => ({
    ...item,
    isShow: index === 0
  }))
  
  lang.value = langList.value[0]
}
//语言切换
const handleChangeLang = async () => {
  const currentIndex = langList.value.findIndex(item => item.isShow)
  const nextIndex = (currentIndex + 1) % langList.value.length
  langList.value = langList.value.map((item, index) => {
    item.isShow = index === nextIndex
    return item
  })
  lang.value = langList.value[nextIndex]

  localStorage.setItem('langList', JSON.stringify(langList.value))
  localStorage.setItem('lang', JSON.stringify(lang.value))
  var restartSox = true
  if (microphone.value && microphone.value.name == 'System Audio') {
    restartSox = false
  }
  if (isStart.value) {
    isStart.value = false;
    await stopAsr(restartSox)
    while (startAsrLoading.value) {
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    startAsr(false, restartSox)
  }
}
const changeColor = (newColor) => {
  color.value = newColor
  store.commit('setColor', color)
  handelSaveSetting()
}
const changeFontSize = (newFontSize) => {
  fontSize.value = newFontSize
  store.commit('setFontSize', fontSize)
  handelSaveSetting()
}
const changeTheme = (value) => {
  if (!value) {
    document.querySelector('html').classList.add('dark')
  } else {
    document.querySelector('html').classList.remove('dark')
  }
  theme.value = !theme.value
  store.commit('setTheme', value)
  handelSaveSetting()
}
const handelSaveSetting = () => {
  saveSetting({
    id: localStorage.getItem('settingId'),
    userId: JSON.parse(localStorage.getItem('userData')).userId,
    email: localStorage.getItem('emailAddress'),
    theme: theme.value,
    fontSize: fontSize.value,
    glossaryHighlightColor: color.value
  })
}
// 保存用户选择的词汇场景的方法
const saveGlossaryScene = (value) => {
  localStorage.setItem('glossarySceneId', value)
}
// 保存用户选择的翻译场景的方法
const saveReplaceScene = (value) => {
  localStorage.setItem('replaceSceneId', value)
}
// 选择词汇场景的方法
const changeGlossaryScene = (value) => {
  console.log('changeGlossaryScene ', value)
  glossarySceneId.value = value
  if (!glossarySceneId.value) {
    glossaryRes.value = []
    return
  }
  saveGlossaryScene(value)
  updateGlossarySceneInfo()
}
const updateGlossarySceneInfo = () => {
  // 更新glossaryRes的值
  getGlossaryByIds({
    startPage: 0,
    pageNum: 9999999,
    searchInfo: '',
    ids: glossarySceneId.value
  }).then(res => {
    glossaryRes.value = res.data.data
  })
}
// 选择替换场景的方法
const changeReplaceScene = (value) => {
  replaceSceneId.value = value
  if (!replaceSceneId.value) {
    replaceRes.value = []
    return
  }
  saveReplaceScene(value)
  // 更新replaceRes的值
  getReplace({
    startPage: 0,
    pageNum: 9999999,
    searchInfo: '',
    sceneId: replaceSceneId.value
  }).then(res => {
    replaceRes.value = res.data.data
  })
}
// 用于当词汇页新增、修改、删除、上传的时候，检测是否为已选择的场景摧毁
// 如果是的话，需要实时更新glossaryRes，来去更新翻译数据集
// targetId: 数据变化的词汇页 所属的场景id
const checkIsGlossaryScene = (targetId) => {
  // 数据变化的词汇页所属的场景id 和 用户选择的场景id 相同时，去更新glossaryRes，然后调用watch，更新数据集
  if (glossarySceneId.value.includes(targetId)) {
    getGlossaryByIds({
      startPage: 0,
      pageNum: 9999999,
      searchInfo: '',
      ids: glossarySceneId.value
    }).then(res => {
      glossaryRes.value = res.data.data
    })
  }
}
// 用于当替换页新增、修改、删除、上传的时候，检测是否为已选择的场景摧毁
// 如果是的话，需要实时更新replaceRes，来去更新翻译数据集
// targetId: 数据变化的词汇页 所属的场景id
const checkIsReplaceScene = (targetId) => {
  // 数据变化的替换页所属的场景id 和 用户选择的场景id 相同时，去更新replaceRes，然后调用watch，更新数据集
  if (targetId === replaceSceneId.value) {
    getReplace({
      startPage: 0,
      pageNum: 9999999,
      searchInfo: '',
      sceneId: replaceSceneId.value
    }).then(res => {
      replaceRes.value = res.data.data
    })
  }
}
const clearGlossarySceneId = (id) => {
  if (id) {
    glossarySceneId.value = glossarySceneId.value.filter(item => item !== id);
  } else {
    glossarySceneId.value = null
  }
  saveGlossaryScene(glossarySceneId.value)
  updateGlossarySceneInfo()
}
const clearReplaceSceneId = () => {
  replaceSceneId.value = null
  saveReplaceScene('')
  replaceRes.value = []
}
const clearTranslationRes = () => {
  translationRes.value = []
}
const toggleRealtimeTranslate = () => {
  isRealtimeTranslateOpen.value = !isRealtimeTranslateOpen.value
  if (isRealtimeTranslateOpen.value) {
    openRealtimeTranslate()
  } else {
    ipcRenderer.send('closeRealTimeTranslateWindow')
  }
}
</script>


<style scoped lang="scss">
.slider-demo-block {
  max-width: 600px;
  display: flex;
  align-items: center;
  padding: 5px;
}
.slider-demo-block .demonstration {
  margin-right: 15px;
}
.leftDom {
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
}

.el-divider--vertical {
  height: 100vh;
  position: absolute;
  top: 0;
  left: 50vw;
  margin: 0;
}

.divider {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: none;
}

.rightDom {
  width: calc(50% - 2px);
  position: absolute;
  top: 0;
  right: 0;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: none;
}

.footerBox {
  width: 100%;
  height: 40px;
  display: flex;
  padding: 0 20px;
  justify-content: flex-start;
  align-items: center;

  :deep(.q-btn) {
    font-weight: 400;
  }
}
.footerBox .loudspeaker {
  margin-left: auto;
}

.upload {
  margin-bottom: 10px;
}

.langBtn {
  animation: pulse 0.6s;
}

.langBtn:active {
  animation: none;
}

.microphoneBox {
  margin: 0 12px;
  width: 180px;
}

.microphoneDark {
  background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' preserveAspectRatio='none'><rect fill='green' fill-opacity='1' x='0' y='0' width='30px' height='30px'/></svg>");
  background-position: bottom;
  background-color: #3d3d3d !important;
  background-repeat: no-repeat;
}

.microphone {
  background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' preserveAspectRatio='none'><rect fill='green' fill-opacity='0.5' x='0' y='0' width='30px' height='30px'/></svg>");
  background-position: bottom;
  background-color: #ffffff !important;
  background-repeat: no-repeat;
}

.themeWhite {
  background-position: bottom;
  background-color: #ffffff !important;
  background-repeat: no-repeat;
  color: #07c160;
}

.themeDark {
  background-position: bottom;
  background-color: #3d3d3d !important;
  background-repeat: no-repeat;
  color: #07c160;
}

.themeWhite.active {
  background-color: #07c160 !important;
  color: #ffffff !important;
}

.themeDark.active {
  background-color: #07c160 !important;
  color: #ffffff !important;
}

.topButton {
  position: absolute;
  top: 50%;
  right: 5px;
  z-index: 100;
  touch-action: none; //pointermove事件在0.3s后就不响应，需要加上这个css
  user-select: none;

  width: 44px;
  height: 44px;
  border: 1.4px solid #07c160;
  color: #07c160;
}

.switchButton {
  position: fixed;
  top: 5px;
  left: 50%;
  z-index: 100;
}

.uploading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40dvh;
  width: 30dvw;
  border-radius: 10px;
  box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.1);
  background-color: #07c160;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  span {
    font-size: 18px;
    font-weight: bolder;
    margin-top: 20px;
    color: #f6f6f6;
  }
}
</style>
