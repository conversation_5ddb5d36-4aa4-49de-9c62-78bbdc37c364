/**
 * Windows Live Caption Controller
 */

import { app, ipcMain } from 'electron'
import path from 'path'
const spawn = require('child_process').spawn

export default class WinLiveCaptionController {
  constructor(mainWindow) {
    this.mainWindow = mainWindow // Main window reference
    this.activeProcesses = new Map() // Store active processes
    this.processIdCounter = 0 // Process ID counter
    this.watchProcess = null // Real-time monitoring process
    this.watchCallback = null // Monitoring callback function
  }

  /**
   * Start Live Caption
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async startLiveCaption() {
    return this.executeCommand(['start'])
  }

  /**
   * Stop Live Caption
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async stopLiveCaption() {
    return this.executeCommand(['stop'])
  }

  /**
   * Hide Live Caption window
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async hideLiveCaption() {
    return this.executeCommand(['hide'])
  }

  /**
   * Show Live Caption window
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async showLiveCaption() {
    return this.executeCommand(['show'])
  }

  /**
   * Get current caption content
   * @returns {Promise<{success: boolean, caption?: string, message?: string}>}
   */
  async getCurrentCaption() {
    return this.executeCommand(['get_caption'])
  }

  /**
   * Set recognition language
   * @param {string} language - Language prefix, such as "简体中文", "English", "日语", etc.
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async setLanguage(language) {
    if (!language || typeof language !== 'string') {
      return { success: false, message: 'Language parameter cannot be empty' }
    }
    return this.executeCommand(['set_language', '--lang', language])
  }

  /**
   * Get available language list
   * @param {object} options - Options
   * @param {boolean} options.fast - Whether to use fast scrolling method
   * @param {boolean} options.noTranslate - Whether not to translate language names
   * @param {string} options.apiKey - Microsoft Translator API key (optional)
   * @returns {Promise<{success: boolean, languages?: string[], translations?: object, message?: string}>}
   */
  async getLanguageList(options = {}) {
    const args = ['list_languages']
    
    if (options.fast) {
      args.push('--fast')
    }
    
    if (options.noTranslate) {
      args.push('--no-translate')
    }
    
    if (options.apiKey) {
      args.push('--api-key', options.apiKey)
    }
    
    return this.executeCommand(args)
  }

  /**
   * Enable microphone audio
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async enableMicAudio() {
    return this.executeCommand(['enable_mic_audio'])
  }

  /**
   * Disable microphone audio
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async disableMicAudio() {
    return this.executeCommand(['disable_mic_audio'])
  }

  /**
   * Check if Live Caption window is visible
   * @returns {Promise<{success: boolean, isVisible?: boolean, message?: string}>}
   */
  async isLiveCaptionVisible() {
    return this.executeCommand(['status'])
  }

  /**
   * Start real-time caption monitoring
   * @param {function} callback - Callback function when caption updates, receives {caption: string} parameter
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async startWatchCaption(callback) {
    if (this.watchProcess) {
      return { success: false, message: 'Monitoring process is already running' }
    }

    if (typeof callback !== 'function') {
      return { success: false, message: 'Invalid callback function parameter' }
    }

    return new Promise((resolve, reject) => {
      const pkgPath = this.getControllerPath()
      
      if (!pkgPath) {
        resolve({ success: false, message: 'Current platform does not support Windows Live Caption control' })
        return
      }

      const processId = ++this.processIdCounter
      console.log(`[Process ${processId}] Starting Live Caption monitoring`)

      const args = ['get_caption', '--watch']
      const watchProcess = spawn(pkgPath, args)
      
      this.watchProcess = watchProcess
      this.watchCallback = callback
      this.activeProcesses.set(processId, watchProcess)

      let errorData = ''

      // 监听标准输出
      watchProcess.stdout.on('data', (data) => {
        const output = data.toString().trim()
        if (output) {
          try {
            const lines = output.split('\n')
            lines.forEach(line => {
              if (line.trim()) {
                const result = JSON.parse(line.trim())
                if (result.caption !== undefined) {
                  callback(result)
                }
              }
            })
          } catch (error) {
            console.error(`[Process ${processId}] Failed to parse monitoring output:`, error, 'Raw output:', output)
          }
        }
      })

      // Listen to error output
      watchProcess.stderr.on('data', (data) => {
        errorData += data.toString()
        console.error(`[Process ${processId}] Live Caption monitoring error output:`, data.toString())
      })

      // Listen to process exit
      watchProcess.on('close', (code) => {
        console.log(`[Process ${processId}] Live Caption monitoring process exited with code: ${code}`)
        this.activeProcesses.delete(processId)
        this.watchProcess = null
        this.watchCallback = null

        if (code !== 0) {
          console.error(`[Process ${processId}] Monitoring process exited abnormally: ${errorData}`)
        }
      })

      // Listen to process error
      watchProcess.on('error', (error) => {
        console.error(`[Process ${processId}] Failed to start Live Caption monitoring process:`, error)
        this.activeProcesses.delete(processId)
        this.watchProcess = null
        this.watchCallback = null
        resolve({ success: false, message: 'Failed to start monitoring process: ' + error.message })
      })

      // Process started successfully
      setTimeout(() => {
        if (this.watchProcess) {
          resolve({ success: true, message: 'Caption monitoring started' })
        }
      }, 1000)
    })
  }

  /**
   * Stop real-time caption monitoring
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async stopWatchCaption() {
    if (!this.watchProcess) {
      return { success: false, message: 'No running monitoring process' }
    }

    return new Promise((resolve) => {
      const process = this.watchProcess
      this.watchProcess = null
      this.watchCallback = null

      process.on('close', () => {
        resolve({ success: true, message: 'Caption monitoring stopped' })
      })

      process.kill('SIGTERM')

      // Force kill if process doesn't end within 3 seconds
      setTimeout(() => {
        if (!process.killed) {
          process.kill('SIGKILL')
          resolve({ success: true, message: 'Caption monitoring force stopped' })
        }
      }, 3000)
    })
  }

  /**
   * Generic method for executing commands
   * @param {string[]} args - Command arguments
   * @returns {Promise<object>} Execution result
   */
  async executeCommand(args) {
    return new Promise((resolve, reject) => {
      const pkgPath = this.getControllerPath()

      if (!pkgPath) {
        resolve({ success: false, message: 'Current platform does not support Windows Live Caption control' })
        return
      }

      const processId = ++this.processIdCounter
      console.log(`[Process ${processId}] Executing Live Caption command:`, args)
      const options = { encoding: 'utf-8'};
      const controlProcess = spawn(pkgPath, args)
      this.activeProcesses.set(processId, controlProcess)

      let outputData = ''
      let errorData = ''

      // Function to cleanup process
      const cleanupProcess = () => {
        this.activeProcesses.delete(processId)
      }

      // Listen to standard output
      controlProcess.stdout.on('data', (data) => {
        outputData += data.toString()
        console.log('data ',outputData)

        try {
          const result = JSON.parse(outputData.trim())
          console.log(`[Process ${processId}] Live Caption control result:`, result)
          resolve(result)
        } catch (err) {
          console.error(`[Process ${processId}] JSON parse error:`, err.message)
        }
      })

      // Listen to error output
      controlProcess.stderr.on('data', (data) => {
        errorData += data.toString()
        console.error(`[Process ${processId}] Live Caption control error output:`, data.toString())
      })

      // Listen to process exit
      controlProcess.on('close', (code) => {
        console.log(`[Process ${processId}] Live Caption ${args} exited with code: ${code}`)
        clearTimeout(timeoutId)
        cleanupProcess()

        // Check if this is a 'start' command and send notification to main window
        if (args == 'start' && this.mainWindow && !this.mainWindow.isDestroyed()) {
          console.log(`[Process ${processId}] Sending live-caption-exit notification for start command`)
          this.mainWindow.webContents.send('live-caption-exit', null)
        }

        // if (code === 0) {
        //   try {
        //     // Parse JSON result
        //     const result = JSON.parse(outputData.trim())
        //     console.log(`[Process ${processId}] Live Caption control result:`, result)
        //     resolve(result)
        //   } catch (error) {
        //     console.error(`[Process ${processId}] Failed to parse control result:`, error)
        //     resolve({ success: false, message: 'Failed to parse control result: ' + error.message })
        //   }
        // } else {
        //   resolve({ success: false, message: `Live Caption control failed, exit code: ${code}, error: ${errorData}` })
        // }
      })

      // Listen to process error
      controlProcess.on('error', (error) => {
        console.error(`[Process ${processId}] Failed to start Live Caption control process:`, error)
        clearTimeout(timeoutId)
        cleanupProcess()
        resolve({ success: false, message: 'Failed to start control process: ' + error.message })
      })

      // Set timeout handling
      const timeoutId = setTimeout(() => {
        if (this.activeProcesses.has(processId)) {
          console.log(`[Process ${processId}] Live Caption control timeout, terminating process`)
          controlProcess.kill('SIGTERM')
          cleanupProcess()
          resolve({ success: false, message: 'Live Caption control timeout' })
        }
      }, 30000) // 30 seconds timeout
    })
  }

  /**
   * Get controller tool path
   * @returns {string|null} Controller tool path, returns null if current platform is not supported
   */
  getControllerPath() {
    if (process.platform === 'win32') {
      // Windows version
      return process.env.PROD
        ? path.join(__statics, '../../lib/win_offline_asr/win_offline_asr.exe')
        : path.join(app.getAppPath(), '../../lib/win_offline_asr/win_offline_asr.exe')
    } else {
      // Other platforms not supported
      console.warn('Current platform does not support Windows Live Caption control')
      return null
    }
  }

  /**
   * Check if current platform supports Live Caption control
   * @returns {boolean} Whether supported
   */
  isSupported() {
    return process.platform === 'win32' && this.getControllerPath() !== null
  }

  /**
   * Get supported platform information
   * @returns {object} Platform support information
   */
  getSupportInfo() {
    return {
      currentPlatform: process.platform,
      currentArch: process.arch,
      isSupported: this.isSupported(),
      supportedPlatforms: [
        { platform: 'win32', arch: 'x64', description: 'Windows 64-bit' },
        { platform: 'win32', arch: 'ia32', description: 'Windows 32-bit' }
      ]
    }
  }

  /**
   * Stop all control processes
   */
  stopAllProcesses() {
    console.log(`Stopping all Live Caption control processes, current active processes: ${this.activeProcesses.size}`)

    // Stop monitoring process
    if (this.watchProcess) {
      this.stopWatchCaption()
    }

    // Stop other processes
    for (const [processId, process] of this.activeProcesses) {
      console.log(`Stopping process ${processId}`)
      process.kill('SIGTERM')
    }
    this.activeProcesses.clear()
  }

  /**
   * Stop specified control process
   * @param {number} processId - Process ID
   */
  stopProcess(processId) {
    if (this.activeProcesses.has(processId)) {
      console.log(`Stopping Live Caption control process ${processId}`)
      this.activeProcesses.get(processId).kill('SIGTERM')
      this.activeProcesses.delete(processId)
    }
  }

  /**
   * Get current active control process count
   * @returns {number} Active process count
   */
  getActiveProcessCount() {
    return this.activeProcesses.size
  }

  /**
   * Get all active process ID list
   * @returns {number[]} Process ID list
   */
  getActiveProcessIds() {
    return Array.from(this.activeProcesses.keys())
  }

  /**
   * Check if caption monitoring is active
   * @returns {boolean} Whether monitoring is active
   */
  isWatching() {
    return this.watchProcess !== null
  }

  /**
   * Setup IPC listeners
   * Call this method in electron-main.js to register all related IPC handlers
   */
  setupIpcHandlers() {
    // Start Live Caption
    ipcMain.handle('winLiveCaption-start', async (event) => {
      try {
        console.log('Received start Live Caption request')
        const result = await this.startLiveCaption()
        console.log('Start Live Caption result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to start Live Caption:', error)
        return { success: false, error: error.message }
      }
    })

    // Stop Live Caption
    ipcMain.handle('winLiveCaption-stop', async (event) => {
      try {
        console.log('Received stop Live Caption request')
        const result = await this.stopLiveCaption()
        console.log('Stop Live Caption result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to stop Live Caption:', error)
        return { success: false, error: error.message }
      }
    })

    // Hide Live Caption window
    ipcMain.handle('winLiveCaption-hide', async (event) => {
      try {
        console.log('Received hide Live Caption window request')
        const result = await this.hideLiveCaption()
        console.log('Hide Live Caption window result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to hide Live Caption window:', error)
        return { success: false, error: error.message }
      }
    })

    // Show Live Caption window
    ipcMain.handle('winLiveCaption-show', async (event) => {
      try {
        console.log('Received show Live Caption window request')
        const result = await this.showLiveCaption()
        console.log('Show Live Caption window result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to show Live Caption window:', error)
        return { success: false, error: error.message }
      }
    })

    // Get current caption content
    ipcMain.handle('winLiveCaption-getCurrentCaption', async (event) => {
      try {
        console.log('Received get current caption content request')
        const result = await this.getCurrentCaption()
        console.log('Get current caption content result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to get current caption content:', error)
        return { success: false, error: error.message }
      }
    })

    // Set recognition language
    ipcMain.handle('winLiveCaption-setLanguage', async (event, language) => {
      try {
        console.log('Received set recognition language request:', language)
        const result = await this.setLanguage(language)
        console.log('Set recognition language result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to set recognition language:', error)
        return { success: false, error: error.message }
      }
    })

    // Get available language list
    ipcMain.handle('winLiveCaption-getLanguageList', async (event, options = {}) => {
      try {
        console.log('Received get available language list request:', options)
        const result = await this.getLanguageList(options)
        if(result.languages && result.translations && Object.keys(result.translations).length > 0){
          console.log('Get available language list result:', result)
          return { success: true, data: result }
        }else{
          return { success: false, data: result }
        }
      } catch (error) {
        console.error('Failed to get available language list:', error)
        return { success: false, error: error.message }
      }
    })

    // Enable microphone audio
    ipcMain.handle('winLiveCaption-enableMicAudio', async (event) => {
      try {
        console.log('Received enable microphone audio request')
        const result = await this.enableMicAudio()
        console.log('Enable microphone audio result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to enable microphone audio:', error)
        return { success: false, error: error.message }
      }
    })

    // Disable microphone audio
    ipcMain.handle('winLiveCaption-disableMicAudio', async (event) => {
      try {
        console.log('Received disable microphone audio request')
        const result = await this.disableMicAudio()
        console.log('Disable microphone audio result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to disable microphone audio:', error)
        return { success: false, error: error.message }
      }
    })

    // Start real-time caption monitoring
    ipcMain.handle('winLiveCaption-startWatchCaption', async (event) => {
      try {
        console.log('Received start real-time caption monitoring request')
        const result = await this.startWatchCaption((captionData) => {
          // Send caption data to renderer process
          console.log('captionData ',captionData)
          event.sender.send('winLiveCaption-captionUpdate', captionData)
        })
        console.log('Start real-time caption monitoring result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to start real-time caption monitoring:', error)
        return { success: false, error: error.message }
      }
    })

    // Stop real-time caption monitoring
    ipcMain.handle('winLiveCaption-stopWatchCaption', async (event) => {
      try {
        console.log('Received stop real-time caption monitoring request')
        const result = await this.stopWatchCaption()
        console.log('Stop real-time caption monitoring result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to stop real-time caption monitoring:', error)
        return { success: false, error: error.message }
      }
    })

    // Get supported platform information
    ipcMain.handle('winLiveCaption-getSupportInfo', async (event) => {
      try {
        const supportInfo = this.getSupportInfo()
        console.log('Windows Live Caption support info:', supportInfo)
        return { success: true, data: supportInfo }
      } catch (error) {
        console.error('Failed to get Windows Live Caption support info:', error)
        return { success: false, error: error.message }
      }
    })

    // Check if caption monitoring is active
    ipcMain.handle('winLiveCaption-isWatching', async (event) => {
      try {
        const isWatching = this.isWatching()
        console.log('Windows Live Caption monitoring status:', isWatching)
        return { success: true, data: { isWatching } }
      } catch (error) {
        console.error('Failed to check Windows Live Caption monitoring status:', error)
        return { success: false, error: error.message }
      }
    })

    // Get active process information
    ipcMain.handle('winLiveCaption-getProcessInfo', async (event) => {
      try {
        const processInfo = {
          activeProcessCount: this.getActiveProcessCount(),
          activeProcessIds: this.getActiveProcessIds(),
          isWatching: this.isWatching()
        }
        console.log('Windows Live Caption process info:', processInfo)
        return { success: true, data: processInfo }
      } catch (error) {
        console.error('Failed to get Windows Live Caption process info:', error)
        return { success: false, error: error.message }
      }
    })

    // Check if Live Caption window is visible
    ipcMain.handle('winLiveCaption-isVisible', async (event) => {
      try {
        console.log('Received check Live Caption visibility request')
        const result = await this.isLiveCaptionVisible()
        console.log('Live Caption visibility check result:', result)
        return { success: true, data: result }
      } catch (error) {
        console.error('Failed to check Live Caption visibility:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('Windows Live Caption IPC listeners setup completed')
  }
}
